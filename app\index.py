from flask import Flask, request
import json
import subprocess
import urllib.request
import xlrd
import re
import requests
from datetime import datetime
from openpyxl import load_workbook
from io import BytesIO
import pandas as pd
from google.cloud import storage
from mimetypes import MimeTypes

import os

os.environ["GOOGLE_APPLICATION_CREDENTIALS"] = "config/credentials.json"

app = Flask(__name__)
baseStorage = 'https://storage.googleapis.com/gides/'

def upload_file(filename, filepath):
    storage_client = storage.Client()
    bucket = storage_client.bucket('gides')

    with open(filepath, 'rb') as file:
        file = file.read()

    mime = MimeTypes()
    mime_type = mime.guess_type(filename)[0]

    blob = bucket.blob(filename)
    blob.upload_from_string(file, content_type=mime_type)

    return True

def extract_rt_rw(address):
    parts = address.split()
    rt = None
    rw = None
    for part in parts:
        if "RT" in part:
            # validate rt must number
            if part.replace("RT", "").strip().isnumeric():
                rt = part.replace("RT", "").strip()

        elif "RW" in part:
            # validate rw must number
            if part.replace("RW", "").strip().isnumeric():
                rw = part.replace("RW", "").strip()

    if rt == None:
        rt = re.search(r'RT\s*:\s*(\d+)', address)

        if rt:
            rt = rt.group(1)

        if rt == None:
            rt = re.search(r'RT\s(\d+)', address)

            if rt:
                rt = rt.group(1)

        if rt == None:
            rt = re.search(r'RT.(\d+)', address)

            if rt:
                rt = rt.group(1)

    if rw == None:
        rw = re.search(r'RW\s*:\s*(\d+)', address)

        if rw:
            rw = rw.group(1)

        if rw == None:
            rw = re.search(r'RW\s(\d+)', address)

            if rw:
                rw = rw.group(1)

        if rw == None:
            rw = re.search(r'RW.(\d+)', address)

            if rw:
                rw = rw.group(1)

    return (rt, rw)


def remove_symbols(input_string, symbols):
    for symbol in symbols:
        if input_string != None:
            input_string = input_string.replace(symbol, '')

    return input_string


def is_xlsx(file_name):
    if file_name.endswith('.xlsx'):
        return True
    else:
        return False


def open_workbook_xlsx(url):
    response = requests.get(url)
    content = response.content
    workbook = load_workbook(filename=BytesIO(content))
    return workbook

def validate_date(date_str, format='%Y-%m-%d'):
    try:
        datetime.strptime(str(date_str), format)
        return True
    except ValueError:
        return False
    
def convert_date(date_str, format='%Y-%m-%d'):
    date_obj = datetime.strptime(str(date_str), format)
    return date_obj.strftime("%Y-%m-%d")

def isfloat(num):
    try:
        float(num)
        return True
    except:
        return False
    
def extract_nik(string):
    result = ''
    for i in range(0, len(string), 2):
        result += string[i:i+2] + ' '
    return result[:-1] + ' ' + string[-2:]

def bip_parser_file(file_path):
    """
    Parse BIP data from a local file instead of URL

    Args:
        file_path (str): Path to the local Excel file

    Returns:
        str: JSON string containing parsed BIP data
    """
    # start = time.time()

    library = 'xlrd'
    file_name = file_path
    try:
        ps = xlrd.open_workbook(file_name)
    except:
        library = 'openpyxl'
        ps = load_workbook(filename=file_name)

    validated_column = ['no', 'nik', 'no kk', 'alamat', 'nama', 'jk', 'tmpt lhr', 'tgl lhr', 'tgl lahir', 'g. drh',
                        'agama', 'status', 'hub. kel', 'pendidikan', 'pekerjaan', 'nama ibu', 'nama ayah',
                        'ket', 'nama_lengkap', 'jenis_klmin', 'tempat_lahir', 'tgl_lhr', 'gol_darah',
                        'status_kawin', 'shdk', 'pendidikan_akhir', 'nama_ayah', 'nama_ibu', 'no_kk', 'no_rt', 'no_rw',
                        'tanggal lahir', 'nama penduduk', 'tempat lahir', 'status kawin', 'pendidikan kk',
                        'rt', 'rw', 'pddk_akhir', 'tmpt_lhr', 'status hubungan', 'nama lengkap', 'tgl. lahir',
                        'lk/pr', 'jenis kelamin(l/p)', 'nomor kk', 'jenis kelamin l/p', 'status perkawinan b/s/p',
                        'lulusan', 'status perkawinan', 'nkk', 'kelamin', 'dusun/alamat', 'no. kk :', 'j.k (l/p)',
                        'nomor nik', 'nama_lgkp', 'stat_kwn', 'stat_hbkel', 'pddk_akh', 'jenis_pkrjn',
                        'nama_lgkp_ibu', 'nama_lgkp_ayah', 'kawin', 'hub. keluarga', 'pendidikan (dlm kk)',
                        'jenis kelamin', 'jenis pekerjaan', 'no. kk', 'ibu', 'tgl/bln/th', 'pendidikan dalam kk',
                        'status hubungan dalam keluarga', 'n i k', 'k k', 'no.rt/rw :',
                        'ket kk', 'pr', 'lk', 'no. nik', 'tempat/tgl. lahir', 'no ktp', 'tanggal', 'tempat',
                        'k', 'd', 'j', 'bk', 'pendidikan terakhir', 'kk', 'ttl', 'l', 'p', 'tempatlahir', 'tanggallahir', 'no. kartu keluarga',
                        'nama anggota keluarga', 'kode keluarga', 'hubungan', 'nama lengkap/ panggilan', 'status perka winan',
                        'kedudukan dlm keluarga', 'alamat lengkap', 'nomor ktp/nik', 'nama lengkap /', 'kedudukan dalam keluarga',
                        'pendidikan tertinggi yang ditamatkan:', 'agama_1', 'tl']

    bip = []

    xd = pd.ExcelFile(file_name)

    for sheetindex in xd.sheet_names:
        sheet = ps[sheetindex]

        max_row = 0
        max_column = 0

        if library == 'openpyxl':
            max_row = sheet.max_row
            max_column = sheet.max_column

        if library == 'xlrd':
            max_row = sheet.nrows
            max_column = sheet.ncols

        if max_row == 0 or max_column == 0:
            continue

        if max_column > 150:
            continue

        index = 0

        kkcol = False
        alamatcol = False

        fixed_kk_index_row = 0
        fixed_nik_index_row = 0
        fixed_nama_index_row = 0
        fixed_jk_index_row = 0
        fixed_tmplahir_index_row = 0
        fixed_tgllahir_index_row = 0
        fixed_grouped_tgllahir_index_row = 0
        fixed_gdarah_index_row = 0
        fixed_agama_index_row = 0
        fixed_status_index_row = 0
        fixed_hubkel_index_row = 0
        fixed_pendidikan_index_row = 0
        fixed_pekerjaan_index_row = 0
        fixed_ibu_index_row = 0
        fixed_ayah_index_row = 0
        fixed_alamat_index_row = 0
        fixed_rt_index_row = 0
        fixed_rw_index_row = 0
        fixed_ket_kk_row = 0

        side_kk = False
        current_side_kk = ''

        side_alamat = False
        current_side_alamat = ''

        side_rtrw = False
        current_side_rtrw = ''

        indicator_kk = False
        indicator_kk_value = ''

        col_pr = 0
        col_lk = 0

        multi_status_col = False
        col_kawin = 0
        col_janda = 0
        col_duda = 0
        col_belumkawin = 0

        backup_current_kk = ''
        backup_current_alamat = ''

        for row in sheet:
            index_row = 0
            nik_row = None

            alamatidx = 0

            current_kk = ''
            current_alamat = ''

            multi_gender_col = False

            for rows in row:
                col = " ".join(str(rows.value).strip().lower().split())

                if (col in validated_column or 'no. kk' in col or 'alamat' in col):
                    if col == 'no kk' or col == 'no_kk' or col == 'nomor kk' or col == 'nkk' or col == 'no. kk :' or 'no. kk' in col or col == 'k k' or col == 'kk' or col == 'no. kartu keluarga' or col == 'kode keluarga':
                        if fixed_kk_index_row == 0:
                            fixed_kk_index_row = index_row

                            if not str(row[fixed_kk_index_row].value).lower() in ['kk', 'no kk', 'nomor kk']:
                                temp_kk = str(
                                    row[fixed_kk_index_row].value).replace("'", "")
                                temp_kk = temp_kk.replace(".", "")
                                temp_kk = temp_kk.replace("_", "")
                                temp_kk = temp_kk.strip()

                                if not temp_kk.isnumeric():
                                    next_row = row[index_row + 1].value

                                    if next_row != None and str(next_row).isnumeric() and len(str(next_row).strip()) == 16:
                                        fixed_kk_index_row = index_row + 1
                                        temp_kk = str(next_row)

                                        if temp_kk.isnumeric():
                                            side_kk = True
                                            current_side_kk = row[index_row + 1].value
                                    elif 'no. kk' in col and col != 'no. kk':
                                        current_side_kk = re.search(
                                            r'\d+', col)

                                        if current_side_kk != None:
                                            current_side_kk = current_side_kk.group()
                                            side_kk = True
                                else:
                                    side_kk = False
                            else:
                                side_kk = False

                    if col == 'nik' or col == 'nomor nik' or col == 'n i k' or col == 'no. nik' or col == 'no ktp' or col == 'nomor ktp/nik':
                        nik_row = index_row
                        fixed_nik_index_row = index_row

                    if col == 'nama' or col == 'nama_lengkap' or col == 'nama penduduk' or col == 'nama lengkap' or col == 'nama_lgkp' or col == 'nama anggota keluarga' or col == 'nama lengkap/ panggilan' or col == 'nama lengkap /':
                        fixed_nama_index_row = index_row

                    if col == 'jk' or col == 'jenis_klmin' or col == 'lk/pr' or col == 'jenis kelamin(l/p)' or col == 'jenis kelamin l/p' or col == 'kelamin' or col == 'j.k (l/p)' or col == 'jenis kelamin':
                        fixed_jk_index_row = index_row

                    if col == 'tmpt lhr' or col == 'tempat_lahir' or col == 'tempat lahir' or col == 'tmpt_lhr' or col == 'tempat' or col == 'tempatlahir':
                        fixed_tmplahir_index_row = index_row

                    if col == 'tgl lhr' or col == 'tgl_lhr' or col == 'tanggal lahir' or col == 'tgl. lahir' or col == 'tgl lahir' or col == 'tgl/bln/th' or col == 'tanggal' or col == 'tanggallahir' or col == 'tl':
                        fixed_tgllahir_index_row = index_row

                    if col == 'g. drh' or col == 'gol_darah':
                        fixed_gdarah_index_row = index_row

                    if col == 'agama' or col == 'agama_1':
                        fixed_agama_index_row = index_row

                    if col == 'status' or col == 'status_kawin' or col == 'status kawin' or col == 'status perkawinan b/s/p' or col == 'status perkawinan' or col == 'stat_kwn' or col == 'kawin' or col == 'status perka winan':
                        if fixed_status_index_row == 0:
                            fixed_status_index_row = index_row

                    if col == 'hub. kel' or col == 'shdk' or col == 'status hubungan' or col == 'stat_hbkel' or col == 'hub. keluarga' or col == 'status hubungan dalam keluarga' or col == 'hubungan' or col == 'kedudukan dlm keluarga' or col == 'kedudukan dalam keluarga':
                        if fixed_hubkel_index_row == 0:
                            fixed_hubkel_index_row = index_row

                    if col == 'pendidikan' or col == 'pendidikan_akhir' or col == 'pendidikan kk' or col == 'pddk_akhir' or col == 'lulusan' or col == 'pddk_akh' or col == 'pendidikan (dlm kk)' or col == 'pendidikan dalam kk' or col == 'pendidikan terakhir' or col == 'pendidikan tertinggi yang ditamatkan:':
                        fixed_pendidikan_index_row = index_row

                    if col == 'pekerjaan' or col == 'jenis_pkrjn' or col == 'jenis pekerjaan':
                        fixed_pekerjaan_index_row = index_row

                    if col == 'nama ibu' or col == 'nama_lgkp_ibu' or col == 'nama_ibu' or col == 'ibu':
                        fixed_ibu_index_row = index_row

                    if col == 'nama ayah' or col == 'nama_ayah' or col == 'nama_lgkp_ayah':
                        fixed_ayah_index_row = index_row

                    if col == 'alamat' or col == 'dusun/alamat' or 'alamat' in col or col == 'alamat lengkap':
                        if col == 'alamat' or col == 'dusun/alamat' or col == 'alamat lengkap':
                            fixed_alamat_index_row = index_row
                        else:
                            current_side_alamat = col.replace(
                                'alamat :', '').strip()

                            if current_side_alamat == '':
                                next_row = row[index_row + 1].value

                                current_side_alamat = str(next_row).strip()
                                side_alamat = True

                    if col == 'no_rt' or col == 'rt' or col == 'no.rt/rw :':
                        if col != 'no.rt/rw :':
                            fixed_rt_index_row = index_row
                        else:
                            try:
                                next_row = row[index_row + 1].value

                                side_rtrw = True
                                current_side_rtrw = str(next_row).strip()
                            except:
                                side_rtrw = False

                    if col == 'no_rw' or col == 'rw' or col == 'no.rt/rw :':
                        if col != 'no.rt/rw :':
                            fixed_rw_index_row = index_row
                        else:
                            try:
                                next_row = row[index_row + 1].value

                                side_rtrw = True
                                current_side_rtrw = str(next_row).strip()
                            except:
                                side_rtrw = False

                    if col == 'ket kk':
                        fixed_ket_kk_row = index_row

                    if col == 'pr' or col == 'p':
                        col_pr = index_row

                    if col == 'lk' or col == 'l':
                        col_lk = index_row

                    if col == 'tempat/tgl. lahir' or col == 'ttl':
                        fixed_grouped_tgllahir_index_row = index_row

                    if col == 'k':
                        col_kawin = index_row

                    if col == 'bk':
                        col_belumkawin = index_row

                    if col == 'j':
                        col_janda = index_row

                    if col == 'd':
                        col_duda = index_row

                index_row += 1

            if col_pr > 0 and col_lk > 0:
                multi_gender_col = True

            if col_kawin > 0 and col_belumkawin > 0 and col_janda > 0 and col_duda > 0:
                multi_status_col = True

            if (nik_row == None or fixed_nik_index_row > 0):
                nik = str(row[fixed_nik_index_row].value).strip().replace(
                    '.', '')
                nik = nik.replace(',', '')
                nik = nik.replace('_', '')
                nik = nik.replace("'", "")

                if (len(nik) == 16 and nik.isnumeric()):
                    if side_kk == False:
                        current_kk = row[fixed_kk_index_row].value

                        if current_kk != None and current_kk != '':
                            try:
                                if sheet[index+2][fixed_kk_index_row].value == None:
                                    backup_current_kk = current_kk
                            except:
                                backup_current_kk = current_kk

                        if current_kk == '' and fixed_ket_kk_row > 0 and str(row[fixed_ket_kk_row].value).strip() == '1.0':
                            for c in range(0, index_row):
                                if (len(str(row[c].value).strip()) == 16):
                                    indicator_kk_value = str(
                                        row[c].value).strip()
                                    indicator_kk = True
                                    break

                            continue

                        if current_kk == '' and indicator_kk == True:
                            current_kk = indicator_kk_value

                        if current_kk != '' and current_kk != None:
                            current_kk = str(current_kk).replace('.', '')
                            current_kk = current_kk.replace('_', '')
                            current_kk = current_kk.replace("'", "")

                        if len(str(current_kk).strip()) != 16:
                            try:
                                head = sheet[index - 2]
                                col = sheet[index - 3]

                                for c in col:
                                    if (c.value.lower() == 'no.kk'):
                                        kkcol = True
                                        break
                                    else:
                                        kkcol = False

                                for c in col:
                                    if (c.value.lower() == 'alamat'):
                                        alamatcol = True
                                        break
                                    else:
                                        alamatidx += 1
                                        alamatcol = False

                                hidx = 0
                                for h in head:
                                    if (len(str(h.value)) == 16 and h.value.isnumeric() and kkcol == True):
                                        current_kk = h.value
                                        backup_current_kk = h.value

                                    if (alamatcol == True):
                                        if (hidx == alamatidx):
                                            current_alamat = h.value
                                            backup_current_alamat = h.value

                                    hidx += 1
                            except:
                                current_kk = ''
                                current_alamat = ''
                    else:
                        current_kk = current_side_kk

                    if current_alamat == '' and fixed_alamat_index_row > 0:
                        current_alamat = row[fixed_alamat_index_row].value

                    if side_alamat == True and current_alamat == '':
                        current_alamat = current_side_alamat

                    if current_kk == None or current_kk == '':
                        current_kk = backup_current_kk

                    validate_current_alamat = False

                    # cek apakah alamat berupa angka
                    try:
                        current_alamat = float(current_alamat)
                        validate_current_alamat = True
                    except:
                        validate_current_alamat = False

                    if (current_alamat == None or current_alamat == '') or validate_current_alamat:
                        current_alamat = backup_current_alamat

                    if nik != None:
                        nik = str(nik).strip()

                    kk = current_kk
                    kk = kk.replace(',', '')
                    
                    # replace ' to null
                    kk = kk.replace("'", "")

                    if kk != None:
                        kk = str(kk).strip()

                    nama = row[fixed_nama_index_row].value

                    jk = None

                    extractnik = extract_nik(nik).split(' ')
                    tanggallahir = extractnik[3]

                    if int(tanggallahir) > 40:
                        jk = 'Perempuan'
                    else:
                        jk = 'Laki-laki'

                    if multi_gender_col:
                        lakilaki = str(row[col_lk].value).strip()
                        perempuan = str(row[col_pr].value).strip()

                        if lakilaki == '1.0' or lakilaki == '1':
                            jk = 'Laki-laki'

                        if jk == None:
                            if perempuan == '1.0' or perempuan == '1':
                                jk = 'Perempuan'

                    if jk == None and row[fixed_jk_index_row].value != None:
                        jk = " ".join(str(
                            row[fixed_jk_index_row].value).split()).lower()

                        jk = (jk == 'lk' or jk ==
                              'l' or jk == 'laki-laki' or jk == 'laki-laki' or jk == '1' or jk == '1.0') and 'Laki-laki' or 'Perempuan'

                    tmplahir = ''
                    tglahir = ''

                    if fixed_grouped_tgllahir_index_row == 0:
                        tmplahir = row[fixed_tmplahir_index_row].value

                        tglahir = row[fixed_tgllahir_index_row].value

                        if tglahir != None:
                            if '|' in str(tglahir):
                                tglahir = tglahir.replace('|', '-')

                            if '/' in str(tglahir):
                                tglahir = tglahir.replace('/', '-')

                            try:
                                tglahir = str(xlrd.xldate_as_datetime(
                                    tglahir, ps.datemode).date())
                            except:
                                try:
                                    tglahir = tglahir.strftime(
                                        '%Y-%m-%d')
                                except:
                                    try:
                                        tglahir = convert_date(tglahir)
                                    except:
                                        tglahir = tglahir

                        if tglahir != None and (validate_date(tglahir) == True or validate_date(tglahir, '%d-%m-%Y') == True):
                            if validate_date(tglahir) == True:
                                tglahir = convert_date(tglahir)

                            if validate_date(tglahir, '%d-%m-%Y') == True:
                                tglahir = convert_date(tglahir, '%d-%m-%Y')
                    else:
                        tmptgllahir = row[fixed_grouped_tgllahir_index_row].value

                        pattern = r'^\w[\w.\s]+\w,\s\d{2}-\d{2}-\d{4}$'
                        pattern2 = r'^\w[\w.\s]+\w,\d{2}-\d{2}-\d{4}$'
                        pattern3 = r'^\w[\w.\s]+\w\s.\d{2}-\d{2}-\d{4}$'
                        pattern4 = r'^\w[\w.\s]+\w;\d{2}-\d{2}-\d{4}$'
                        pattern5 = r'^.\w[\w.\s]+,\d{2}-\d{2}-\d{4}'
                        pattern6 = r'^.\w[\w.\s]+,\d{2}.-\d{2}-\d{4}'

                        try:
                            if tmptgllahir != None and (re.match(pattern, tmptgllahir) or re.match(pattern2, tmptgllahir) or re.match(pattern3, tmptgllahir) or re.match(pattern4, tmptgllahir) or re.match(pattern5, tmptgllahir) or re.match(pattern6, tmptgllahir)):
                                if ',' in tmptgllahir:
                                    tmplahir = tmptgllahir.split(',')[0]
                                elif '.' in tmptgllahir:
                                    tmplahir = tmptgllahir.split('.')[0]
                                else:
                                    tmplahir = tmptgllahir.split(';')[0]

                                if ',' in tmptgllahir:
                                    tglahir = tmptgllahir.split(',')[1].strip()

                                    if '.' in tglahir:
                                        tglahir = tglahir.replace('.', '')

                                        print(tglahir)
                                elif '.' in tmptgllahir:
                                    tglahir = tmptgllahir.split('.')[1].strip()
                                else:
                                    tglahir = tmptgllahir.split(';')[1].strip()

                                if tglahir != None:
                                    if '|' in str(tglahir):
                                        tglahir = tglahir.replace('|', '-')

                                    if '/' in str(tglahir):
                                        tglahir = tglahir.replace('/', '-')

                                    try:
                                        tglahir = str(xlrd.xldate_as_datetime(
                                            tglahir, ps.datemode).date())
                                    except:
                                        try:
                                            tglahir = tglahir.strftime(
                                                '%Y-%m-%d')
                                        except:
                                            try:
                                                tglahir = convert_date(tglahir)
                                            except:
                                                tglahir = tglahir

                                if tglahir != None and (validate_date(tglahir) == True or validate_date(tglahir, '%d-%m-%Y') == True):
                                    if validate_date(tglahir) == True:
                                        tglahir = convert_date(tglahir)

                                    if validate_date(tglahir, '%d-%m-%Y') == True:
                                        tglahir = convert_date(
                                            tglahir, '%d-%m-%Y')
                            elif validate_date(tmptgllahir, '%d/%m/%Y') == True:
                                tglahir = convert_date(tmptgllahir, '%d/%m/%Y')
                                tmplahir = row[fixed_tmplahir_index_row].value
                        except:
                            tmplahir = ''
                            tglahir = ''

                    darah = str(row[fixed_gdarah_index_row].value)

                    if darah != None and len(darah) > 2:
                        darah = '-'

                    if fixed_gdarah_index_row == 0:
                        darah = '-'

                    agama = row[fixed_agama_index_row].value

                    if fixed_agama_index_row == 0:
                        agama = None

                    if fixed_status_index_row != 0:
                        status = str(row[fixed_status_index_row].value)
                    else:
                        status = None

                    if multi_status_col == True:
                        if str(row[col_kawin].value).strip() == '1':
                            status = 'Kawin'

                        if str(row[col_belumkawin].value).strip() == '1':
                            status = 'Belum Kawin'

                        if str(row[col_janda].value).strip() == '1':
                            status = 'Janda'

                        if str(row[col_duda].value).strip() == '1':
                            status = 'Duda'

                    if fixed_hubkel_index_row != 0:
                        hubkel = row[fixed_hubkel_index_row].value
                    else:
                        hubkel = None

                    if fixed_pendidikan_index_row != 0:
                        pendidikan = row[fixed_pendidikan_index_row].value
                    else:
                        pendidikan = None

                    pekerjaan = row[fixed_pekerjaan_index_row].value

                    if fixed_pekerjaan_index_row == 0:
                        pekerjaan = None

                    if fixed_ibu_index_row != 0:
                        ibu = row[fixed_ibu_index_row].value
                    else:
                        ibu = None

                    if fixed_ayah_index_row != 0:
                        ayah = row[fixed_ayah_index_row].value
                    else:
                        ayah = None

                    if side_rtrw == False:
                        try:
                            rt, rw = extract_rt_rw(current_alamat)
                        except:
                            rt, rw = None, None
                    else:
                        try:
                            split_rtrw = current_side_rtrw.split('/')

                            if len(split_rtrw) > 1:
                                rt = split_rtrw[0]
                                rw = split_rtrw[1]
                        except:
                            rt, rw = None, None

                    removed_chars = [':', ',']
                    rw = remove_symbols(rw, removed_chars)
                    rt = remove_symbols(rt, removed_chars)

                    try:
                        if rt == None and rw == None:
                            regex_rt = re.compile(
                                r'rt\/rw\s:\s(\w{1,3})\/\S{1,3}')
                            matches = regex_rt.search(current_alamat)

                            if matches:
                                split_rtrw = matches.group().split(':')
                                number_ofrtrw = split_rtrw[1].split('/')

                                rt = number_ofrtrw[0]
                                rw = number_ofrtrw[1]
                    except:
                        rt, rw = None, None

                    if rt == None or rt == '':
                        if row[fixed_rt_index_row].value != None and fixed_rt_index_row != 0:
                            rt, _ = extract_rt_rw(
                                str(row[fixed_rt_index_row].value))

                            if rt == None or rt == '':
                                rt = row[fixed_rt_index_row].value
                        else:
                            rt = None

                    if rw == None or rw == '':
                        if row[fixed_rw_index_row].value != None:
                            _, rw = extract_rt_rw(
                                str(row[fixed_rw_index_row].value))

                            if rw == None or rw == '':
                                rw = row[fixed_rw_index_row].value
                        else:
                            rw = None

                    # if fixed_rw_index_row == 0:
                    #     rw = None

                    if nama != None:
                        nama = nama.strip()

                    if tmplahir != None:
                        if not str(tmplahir).isnumeric() and isfloat(tmplahir) == False:
                            tmplahir = str(tmplahir).strip()
                        else:
                            tmplahir = None

                    if tglahir != None:
                        tglahir = str(tglahir).strip()

                    if validate_date(tglahir) == False:
                        tglahir = None

                    if darah != None:
                        darah = darah.strip()

                    if agama != None:
                        agama = str(agama).strip()

                    if status != None:
                        status = status.strip()

                    if hubkel != None:
                        hubkel = str(hubkel).strip()

                    try:
                        if pendidikan != None:
                            pendidikan = str(pendidikan).strip()
                    except:
                        pendidikan = None

                    if pekerjaan != None:
                        pekerjaan = str(pekerjaan).strip()

                    if rt != None:
                        rt = str(rt).strip()

                    if rw != None:
                        rw = str(rw).strip()

                    if ibu != None:
                        ibu = ibu.strip()

                    if ayah != None:
                        ayah = ayah.strip()

                    if kk == '' or kk.isnumeric() == False:
                        continue

                    if nik == '' or nik.isnumeric() == False:
                        continue

                    if nama == '' or nama == None or nama.isnumeric() == True:
                        continue

                    bip.append({
                        'kk': kk,
                        'nik': nik,
                        'nama': nama,
                        'jk': jk,
                        'tmplahir': tmplahir,
                        'tglahir': tglahir,
                        'darah': darah,
                        'agama': agama,
                        'status': status,
                        'hubkel': hubkel,
                        'pendidikan': pendidikan,
                        'pekerjaan': pekerjaan,
                        'alamat': current_alamat,
                        'rt': rt,
                        'rw': rw,
                        'ibu': ibu,
                        'ayah': ayah
                    })

            index += 1

    # end = time.time()

    # print(end - start)

    return json.dumps(bip)

def bip_parser(url):
    # start = time.time()

    library = 'xlrd'
    file_name, headers = urllib.request.urlretrieve(url)
    try:
        ps = xlrd.open_workbook(file_name)
    except:
        library = 'openpyxl'
        ps = open_workbook_xlsx(url)

    validated_column = ['no', 'nik', 'no kk', 'alamat', 'nama', 'jk', 'tmpt lhr', 'tgl lhr', 'tgl lahir', 'g. drh',
                        'agama', 'status', 'hub. kel', 'pendidikan', 'pekerjaan', 'nama ibu', 'nama ayah',
                        'ket', 'nama_lengkap', 'jenis_klmin', 'tempat_lahir', 'tgl_lhr', 'gol_darah',
                        'status_kawin', 'shdk', 'pendidikan_akhir', 'nama_ayah', 'nama_ibu', 'no_kk', 'no_rt', 'no_rw',
                        'tanggal lahir', 'nama penduduk', 'tempat lahir', 'status kawin', 'pendidikan kk',
                        'rt', 'rw', 'pddk_akhir', 'tmpt_lhr', 'status hubungan', 'nama lengkap', 'tgl. lahir',
                        'lk/pr', 'jenis kelamin(l/p)', 'nomor kk', 'jenis kelamin l/p', 'status perkawinan b/s/p',
                        'lulusan', 'status perkawinan', 'nkk', 'kelamin', 'dusun/alamat', 'no. kk :', 'j.k (l/p)',
                        'nomor nik', 'nama_lgkp', 'stat_kwn', 'stat_hbkel', 'pddk_akh', 'jenis_pkrjn',
                        'nama_lgkp_ibu', 'nama_lgkp_ayah', 'kawin', 'hub. keluarga', 'pendidikan (dlm kk)',
                        'jenis kelamin', 'jenis pekerjaan', 'no. kk', 'ibu', 'tgl/bln/th', 'pendidikan dalam kk',
                        'status hubungan dalam keluarga', 'hubngan dlm kelarga', 'n i k', 'k k', 'no.rt/rw :',
                        'ket kk', 'pr', 'lk', 'no. nik', 'tempat/tgl. lahir', 'no ktp', 'tanggal', 'tempat',
                        'k', 'd', 'j', 'bk', 'pendidikan terakhir', 'kk', 'ttl', 'l', 'p', 'tempatlahir', 'tanggallahir', 'no. kartu keluarga',
                        'nama anggota keluarga', 'kode keluarga', 'hubungan', 'nama lengkap/ panggilan', 'status perka winan',
                        'kedudukan dlm keluarga', 'alamat lengkap', 'nomor ktp/nik', 'nama lengkap /', 'kedudukan dalam keluarga',
                        'pendidikan tertinggi yang ditamatkan:', 'agama_1', 'tl', 'tempat/tgl/lahir', 'nama orang tua/ayah', 'nama orang tua/ibu']

    bip = []

    xd = pd.ExcelFile(file_name)

    for sheetindex in xd.sheet_names:
        sheet = ps[sheetindex]

        max_row = 0
        max_column = 0

        if library == 'openpyxl':
            max_row = sheet.max_row
            max_column = sheet.max_column

        if library == 'xlrd':
            max_row = sheet.nrows
            max_column = sheet.ncols

        if max_row == 0 or max_column == 0:
            continue

        if max_column > 150:
            continue

        index = 0

        kkcol = False
        alamatcol = False

        fixed_kk_index_row = 0
        fixed_nik_index_row = 0
        fixed_nama_index_row = 0
        fixed_jk_index_row = 0
        fixed_tmplahir_index_row = 0
        fixed_tgllahir_index_row = 0
        fixed_grouped_tgllahir_index_row = 0
        fixed_gdarah_index_row = 0
        fixed_agama_index_row = 0
        fixed_status_index_row = 0
        fixed_hubkel_index_row = 0
        fixed_pendidikan_index_row = 0
        fixed_pekerjaan_index_row = 0
        fixed_ibu_index_row = 0
        fixed_ayah_index_row = 0
        fixed_alamat_index_row = 0
        fixed_rt_index_row = 0
        fixed_rw_index_row = 0
        fixed_ket_kk_row = 0

        side_kk = False
        current_side_kk = ''

        side_alamat = False
        current_side_alamat = ''

        side_rtrw = False
        current_side_rtrw = ''

        indicator_kk = False
        indicator_kk_value = ''

        col_pr = 0
        col_lk = 0

        multi_status_col = False
        col_kawin = 0
        col_janda = 0
        col_duda = 0
        col_belumkawin = 0

        backup_current_kk = ''
        backup_current_alamat = ''

        for row in sheet:
            index_row = 0
            nik_row = None

            alamatidx = 0

            current_kk = ''
            current_alamat = ''

            multi_gender_col = False

            for rows in row:
                col = " ".join(str(rows.value).strip().lower().split())

                if (col in validated_column or 'no. kk' in col or 'alamat' in col):
                    if col == 'no kk' or col == 'no_kk' or col == 'nomor kk' or col == 'nkk' or col == 'no. kk :' or 'no. kk' in col or col == 'k k' or col == 'kk' or col == 'no. kartu keluarga' or col == 'kode keluarga':
                        if fixed_kk_index_row == 0:
                            fixed_kk_index_row = index_row

                            if not str(row[fixed_kk_index_row].value).lower() in ['kk', 'no kk', 'nomor kk']:
                                temp_kk = str(
                                    row[fixed_kk_index_row].value).replace("'", "")
                                temp_kk = temp_kk.replace(".", "")
                                temp_kk = temp_kk.replace("_", "")
                                temp_kk = temp_kk.strip()

                                if not temp_kk.isnumeric():
                                    next_row = row[index_row + 1].value

                                    if next_row != None and str(next_row).isnumeric() and len(str(next_row).strip()) == 16:
                                        fixed_kk_index_row = index_row + 1
                                        temp_kk = str(next_row)

                                        if temp_kk.isnumeric():
                                            side_kk = True
                                            current_side_kk = row[index_row + 1].value
                                    elif 'no. kk' in col and col != 'no. kk':
                                        current_side_kk = re.search(
                                            r'\d+', col)

                                        if current_side_kk != None:
                                            current_side_kk = current_side_kk.group()
                                            side_kk = True
                                else:
                                    side_kk = False
                            else:
                                side_kk = False

                    if col == 'nik' or col == 'nomor nik' or col == 'n i k' or col == 'no. nik' or col == 'no ktp' or col == 'nomor ktp/nik':
                        nik_row = index_row
                        fixed_nik_index_row = index_row

                    if col == 'nama' or col == 'nama_lengkap' or col == 'nama penduduk' or col == 'nama lengkap' or col == 'nama_lgkp' or col == 'nama anggota keluarga' or col == 'nama lengkap/ panggilan' or col == 'nama lengkap /':
                        fixed_nama_index_row = index_row

                    if col == 'jk' or col == 'jenis_klmin' or col == 'lk/pr' or col == 'jenis kelamin(l/p)' or col == 'jenis kelamin l/p' or col == 'kelamin' or col == 'j.k (l/p)' or col == 'jenis kelamin':
                        fixed_jk_index_row = index_row

                    if col == 'tmpt lhr' or col == 'tempat_lahir' or col == 'tempat lahir' or col == 'tmpt_lhr' or col == 'tempat' or col == 'tempatlahir':
                        fixed_tmplahir_index_row = index_row

                    if col == 'tgl lhr' or col == 'tgl_lhr' or col == 'tanggal lahir' or col == 'tgl. lahir' or col == 'tgl lahir' or col == 'tgl/bln/th' or col == 'tanggal' or col == 'tanggallahir' or col == 'tl':
                        fixed_tgllahir_index_row = index_row

                    if col == 'g. drh' or col == 'gol_darah':
                        fixed_gdarah_index_row = index_row

                    if col == 'agama' or col == 'agama_1':
                        fixed_agama_index_row = index_row

                    if col == 'status' or col == 'status_kawin' or col == 'status kawin' or col == 'status perkawinan b/s/p' or col == 'status perkawinan' or col == 'stat_kwn' or col == 'kawin' or col == 'status perka winan':
                        if fixed_status_index_row == 0:
                            fixed_status_index_row = index_row

                    if col == 'hub. kel' or col == 'shdk' or col == 'status hubungan' or col == 'stat_hbkel' or col == 'hub. keluarga' or col == 'status hubungan dalam keluarga' or col == 'hubungan' or col == 'kedudukan dlm keluarga' or col == 'kedudukan dalam keluarga' or col == 'hubngan dlm kelarga':
                        if fixed_hubkel_index_row == 0:
                            fixed_hubkel_index_row = index_row

                    if col == 'pendidikan' or col == 'pendidikan_akhir' or col == 'pendidikan kk' or col == 'pddk_akhir' or col == 'lulusan' or col == 'pddk_akh' or col == 'pendidikan (dlm kk)' or col == 'pendidikan dalam kk' or col == 'pendidikan terakhir' or col == 'pendidikan tertinggi yang ditamatkan:':
                        fixed_pendidikan_index_row = index_row

                    if col == 'pekerjaan' or col == 'jenis_pkrjn' or col == 'jenis pekerjaan':
                        fixed_pekerjaan_index_row = index_row

                    if col == 'nama ibu' or col == 'nama_lgkp_ibu' or col == 'nama_ibu' or col == 'ibu' or col == 'nama orang tua/ibu':
                        fixed_ibu_index_row = index_row

                    if col == 'nama ayah' or col == 'nama_ayah' or col == 'nama_lgkp_ayah' or col == 'nama orang tua/ayah':
                        fixed_ayah_index_row = index_row

                    if col == 'alamat' or col == 'dusun/alamat' or 'alamat' in col or col == 'alamat lengkap':
                        if col == 'alamat' or col == 'dusun/alamat' or col == 'alamat lengkap':
                            fixed_alamat_index_row = index_row
                        else:
                            current_side_alamat = col.replace(
                                'alamat :', '').strip()

                            if current_side_alamat == '':
                                next_row = row[index_row + 1].value

                                current_side_alamat = str(next_row).strip()
                                side_alamat = True

                    if col == 'no_rt' or col == 'rt' or col == 'no.rt/rw :':
                        if col != 'no.rt/rw :':
                            fixed_rt_index_row = index_row
                        else:
                            try:
                                next_row = row[index_row + 1].value

                                side_rtrw = True
                                current_side_rtrw = str(next_row).strip()
                            except:
                                side_rtrw = False

                    if col == 'no_rw' or col == 'rw' or col == 'no.rt/rw :':
                        if col != 'no.rt/rw :':
                            fixed_rw_index_row = index_row
                        else:
                            try:
                                next_row = row[index_row + 1].value

                                side_rtrw = True
                                current_side_rtrw = str(next_row).strip()
                            except:
                                side_rtrw = False

                    if col == 'ket kk':
                        fixed_ket_kk_row = index_row

                    if col == 'pr' or col == 'p':
                        col_pr = index_row

                    if col == 'lk' or col == 'l':
                        col_lk = index_row

                    if col == 'tempat/tgl. lahir' or col == 'ttl' or col == 'tempat/tgl/lahir':
                        fixed_grouped_tgllahir_index_row = index_row

                    if col == 'k':
                        col_kawin = index_row

                    if col == 'bk':
                        col_belumkawin = index_row

                    if col == 'j':
                        col_janda = index_row

                    if col == 'd':
                        col_duda = index_row

                index_row += 1

            if col_pr > 0 and col_lk > 0:
                multi_gender_col = True

            if col_kawin > 0 and col_belumkawin > 0 and col_janda > 0 and col_duda > 0:
                multi_status_col = True

            if (nik_row == None or fixed_nik_index_row > 0):
                nik = str(row[fixed_nik_index_row].value).strip().replace(
                    '.', '')
                nik = nik.replace(',', '')
                nik = nik.replace('_', '')
                nik = nik.replace("'", "")

                if (len(nik) == 16 and nik.isnumeric()):
                    if side_kk == False:
                        current_kk = row[fixed_kk_index_row].value

                        if current_kk != None and current_kk != '':
                            try:
                                if sheet[index+2][fixed_kk_index_row].value == None:
                                    backup_current_kk = current_kk
                            except:
                                backup_current_kk = current_kk

                        if current_kk == '' and fixed_ket_kk_row > 0 and str(row[fixed_ket_kk_row].value).strip() == '1.0':
                            for c in range(0, index_row):
                                if (len(str(row[c].value).strip()) == 16):
                                    indicator_kk_value = str(
                                        row[c].value).strip()
                                    indicator_kk = True
                                    break

                            continue

                        if current_kk == '' and indicator_kk == True:
                            current_kk = indicator_kk_value

                        if current_kk != '' and current_kk != None:
                            current_kk = str(current_kk).replace('.', '')
                            current_kk = current_kk.replace('_', '')
                            current_kk = current_kk.replace("'", "")

                        if len(str(current_kk).strip()) != 16:
                            try:
                                head = sheet[index - 2]
                                col = sheet[index - 3]

                                for c in col:
                                    if (c.value.lower() == 'no.kk'):
                                        kkcol = True
                                        break
                                    else:
                                        kkcol = False

                                for c in col:
                                    if (c.value.lower() == 'alamat'):
                                        alamatcol = True
                                        break
                                    else:
                                        alamatidx += 1
                                        alamatcol = False

                                hidx = 0
                                for h in head:
                                    if (len(str(h.value)) == 16 and h.value.isnumeric() and kkcol == True):
                                        current_kk = h.value
                                        backup_current_kk = h.value

                                    if (alamatcol == True):
                                        if (hidx == alamatidx):
                                            current_alamat = h.value
                                            backup_current_alamat = h.value

                                    hidx += 1
                            except:
                                current_kk = ''
                                current_alamat = ''
                    else:
                        current_kk = current_side_kk

                    if current_alamat == '' and fixed_alamat_index_row > 0:
                        current_alamat = row[fixed_alamat_index_row].value

                    if side_alamat == True and current_alamat == '':
                        current_alamat = current_side_alamat

                    if current_kk == None or current_kk == '':
                        current_kk = backup_current_kk

                    validate_current_alamat = False

                    # cek apakah alamat berupa angka
                    try:
                        current_alamat = float(current_alamat)
                        validate_current_alamat = True
                    except:
                        validate_current_alamat = False

                    if (current_alamat == None or current_alamat == '') or validate_current_alamat:
                        current_alamat = backup_current_alamat

                    if nik != None:
                        nik = str(nik).strip()

                    kk = current_kk
                    kk = kk.replace(',', '')

                    # replace ' to null
                    kk = kk.replace("'", "")

                    if kk != None:
                        kk = str(kk).strip()

                    nama = row[fixed_nama_index_row].value

                    jk = None

                    extractnik = extract_nik(nik).split(' ')
                    tanggallahir = extractnik[3]

                    if int(tanggallahir) > 40:
                        jk = 'Perempuan'
                    else:
                        jk = 'Laki-laki'

                    if multi_gender_col:
                        lakilaki = str(row[col_lk].value).strip()
                        perempuan = str(row[col_pr].value).strip()

                        if lakilaki == '1.0' or lakilaki == '1':
                            jk = 'Laki-laki'

                        if jk == None:
                            if perempuan == '1.0' or perempuan == '1':
                                jk = 'Perempuan'

                    if jk == None and row[fixed_jk_index_row].value != None:
                        jk = " ".join(str(
                            row[fixed_jk_index_row].value).split()).lower()

                        jk = (jk == 'lk' or jk ==
                              'l' or jk == 'laki-laki' or jk == 'laki-laki' or jk == '1' or jk == '1.0') and 'Laki-laki' or 'Perempuan'

                    tmplahir = ''
                    tglahir = ''

                    if fixed_grouped_tgllahir_index_row == 0:
                        tmplahir = row[fixed_tmplahir_index_row].value

                        tglahir = row[fixed_tgllahir_index_row].value

                        if tglahir != None:
                            if '|' in str(tglahir):
                                tglahir = tglahir.replace('|', '-')

                            if '/' in str(tglahir):
                                tglahir = tglahir.replace('/', '-')

                            try:
                                tglahir = str(xlrd.xldate_as_datetime(
                                    tglahir, ps.datemode).date())
                            except:
                                try:
                                    tglahir = tglahir.strftime(
                                        '%Y-%m-%d')
                                except:
                                    try:
                                        tglahir = convert_date(tglahir)
                                    except:
                                        tglahir = tglahir

                        if tglahir != None and (validate_date(tglahir) == True or validate_date(tglahir, '%d-%m-%Y') == True):
                            if validate_date(tglahir) == True:
                                tglahir = convert_date(tglahir)

                            if validate_date(tglahir, '%d-%m-%Y') == True:
                                tglahir = convert_date(tglahir, '%d-%m-%Y')
                    else:
                        tmptgllahir = row[fixed_grouped_tgllahir_index_row].value

                        pattern = r'^\w[\w.\s]+\w,\s\d{2}-\d{2}-\d{4}$'
                        pattern2 = r'^\w[\w.\s]+\w,\d{2}-\d{2}-\d{4}$'
                        pattern3 = r'^\w[\w.\s]+\w\s.\d{2}-\d{2}-\d{4}$'
                        pattern4 = r'^\w[\w.\s]+\w;\d{2}-\d{2}-\d{4}$'
                        pattern5 = r'^.\w[\w.\s]+,\d{2}-\d{2}-\d{4}'
                        pattern6 = r'^.\w[\w.\s]+,\d{2}.-\d{2}-\d{4}'

                        try:
                            if tmptgllahir != None and (re.match(pattern, tmptgllahir) or re.match(pattern2, tmptgllahir) or re.match(pattern3, tmptgllahir) or re.match(pattern4, tmptgllahir) or re.match(pattern5, tmptgllahir) or re.match(pattern6, tmptgllahir)):
                                if ',' in tmptgllahir:
                                    tmplahir = tmptgllahir.split(',')[0]
                                elif '.' in tmptgllahir:
                                    tmplahir = tmptgllahir.split('.')[0]
                                else:
                                    tmplahir = tmptgllahir.split(';')[0]

                                if ',' in tmptgllahir:
                                    tglahir = tmptgllahir.split(',')[1].strip()

                                    if '.' in tglahir:
                                        tglahir = tglahir.replace('.', '')

                                        print(tglahir)
                                elif '.' in tmptgllahir:
                                    tglahir = tmptgllahir.split('.')[1].strip()
                                else:
                                    tglahir = tmptgllahir.split(';')[1].strip()

                                if tglahir != None:
                                    if '|' in str(tglahir):
                                        tglahir = tglahir.replace('|', '-')

                                    if '/' in str(tglahir):
                                        tglahir = tglahir.replace('/', '-')

                                    try:
                                        tglahir = str(xlrd.xldate_as_datetime(
                                            tglahir, ps.datemode).date())
                                    except:
                                        try:
                                            tglahir = tglahir.strftime(
                                                '%Y-%m-%d')
                                        except:
                                            try:
                                                tglahir = convert_date(tglahir)
                                            except:
                                                tglahir = tglahir

                                if tglahir != None and (validate_date(tglahir) == True or validate_date(tglahir, '%d-%m-%Y') == True):
                                    if validate_date(tglahir) == True:
                                        tglahir = convert_date(tglahir)

                                    if validate_date(tglahir, '%d-%m-%Y') == True:
                                        tglahir = convert_date(
                                            tglahir, '%d-%m-%Y')
                            elif validate_date(tmptgllahir, '%d/%m/%Y') == True:
                                tglahir = convert_date(tmptgllahir, '%d/%m/%Y')
                                tmplahir = row[fixed_tmplahir_index_row].value
                        except:
                            tmplahir = ''
                            tglahir = ''

                    darah = str(row[fixed_gdarah_index_row].value)

                    if darah != None and len(darah) > 2:
                        darah = '-'

                    if fixed_gdarah_index_row == 0:
                        darah = '-'

                    agama = row[fixed_agama_index_row].value

                    if fixed_agama_index_row == 0:
                        agama = None

                    if fixed_status_index_row != 0:
                        status = str(row[fixed_status_index_row].value)
                    else:
                        status = None

                    if multi_status_col == True:
                        if str(row[col_kawin].value).strip() == '1':
                            status = 'Kawin'

                        if str(row[col_belumkawin].value).strip() == '1':
                            status = 'Belum Kawin'

                        if str(row[col_janda].value).strip() == '1':
                            status = 'Janda'

                        if str(row[col_duda].value).strip() == '1':
                            status = 'Duda'

                    if fixed_hubkel_index_row != 0:
                        hubkel = row[fixed_hubkel_index_row].value
                    else:
                        hubkel = None

                    if fixed_pendidikan_index_row != 0:
                        pendidikan = row[fixed_pendidikan_index_row].value
                    else:
                        pendidikan = None

                    pekerjaan = row[fixed_pekerjaan_index_row].value

                    if fixed_pekerjaan_index_row == 0:
                        pekerjaan = None

                    if fixed_ibu_index_row != 0:
                        ibu = row[fixed_ibu_index_row].value
                    else:
                        ibu = None

                    if fixed_ayah_index_row != 0:
                        ayah = row[fixed_ayah_index_row].value
                    else:
                        ayah = None

                    if side_rtrw == False:
                        try:
                            rt, rw = extract_rt_rw(current_alamat)
                        except:
                            rt, rw = None, None
                    else:
                        try:
                            split_rtrw = current_side_rtrw.split('/')

                            if len(split_rtrw) > 1:
                                rt = split_rtrw[0]
                                rw = split_rtrw[1]
                        except:
                            rt, rw = None, None

                    removed_chars = [':', ',']
                    rw = remove_symbols(rw, removed_chars)
                    rt = remove_symbols(rt, removed_chars)

                    try:
                        if rt == None and rw == None:
                            regex_rt = re.compile(
                                r'rt\/rw\s:\s(\w{1,3})\/\S{1,3}')
                            matches = regex_rt.search(current_alamat)

                            if matches:
                                split_rtrw = matches.group().split(':')
                                number_ofrtrw = split_rtrw[1].split('/')

                                rt = number_ofrtrw[0]
                                rw = number_ofrtrw[1]
                    except:
                        rt, rw = None, None

                    if rt == None or rt == '':
                        if row[fixed_rt_index_row].value != None and fixed_rt_index_row != 0:
                            rt, _ = extract_rt_rw(
                                str(row[fixed_rt_index_row].value))

                            if rt == None or rt == '':
                                rt = row[fixed_rt_index_row].value
                        else:
                            rt = None

                    if rw == None or rw == '':
                        if row[fixed_rw_index_row].value != None:
                            _, rw = extract_rt_rw(
                                str(row[fixed_rw_index_row].value))

                            if rw == None or rw == '':
                                rw = row[fixed_rw_index_row].value
                        else:
                            rw = None

                    # if fixed_rw_index_row == 0:
                    #     rw = None

                    if nama != None:
                        nama = nama.strip()

                    if tmplahir != None:
                        if not str(tmplahir).isnumeric() and isfloat(tmplahir) == False:
                            tmplahir = str(tmplahir).strip()
                        else:
                            tmplahir = None

                    if tglahir != None:
                        tglahir = str(tglahir).strip()

                    if validate_date(tglahir) == False:
                        tglahir = None

                    if darah != None:
                        darah = darah.strip()

                    if agama != None:
                        agama = str(agama).strip()

                    if status != None:
                        status = status.strip()

                    if hubkel != None:
                        hubkel = str(hubkel).strip()

                    try:
                        if pendidikan != None:
                            pendidikan = str(pendidikan).strip()
                    except:
                        pendidikan = None

                    if pekerjaan != None:
                        pekerjaan = str(pekerjaan).strip()

                    if rt != None:
                        rt = str(rt).strip()

                    if rw != None:
                        rw = str(rw).strip()

                    if ibu != None:
                        ibu = ibu.strip()

                    if ayah != None:
                        ayah = ayah.strip()

                    if kk == '' or kk.isnumeric() == False:
                        continue

                    if nik == '' or nik.isnumeric() == False:
                        continue

                    if nama == '' or nama == None or nama.isnumeric() == True:
                        continue

                    bip.append({
                        'kk': kk,
                        'nik': nik,
                        'nama': nama,
                        'jk': jk,
                        'tmplahir': tmplahir,
                        'tglahir': tglahir,
                        'darah': darah,
                        'agama': agama,
                        'status': status,
                        'hubkel': hubkel,
                        'pendidikan': pendidikan,
                        'pekerjaan': pekerjaan,
                        'alamat': current_alamat,
                        'rt': rt,
                        'rw': rw,
                        'ibu': ibu,
                        'ayah': ayah
                    })

            index += 1

    # end = time.time()

    # print(end - start)

    return json.dumps(bip)

def convert_to_pdf(filename):
    cwd = '/app/cache/'
    cmd = 'soffice --headless --convert-to pdf "{filename}"'.format(filename=filename)

    fileUrl = baseStorage + filename
    filePath = cwd + filename

    response = requests.get(fileUrl)

    if response.status_code != 200:
        return json.dumps({
            'error': 'File not found'
        })
    
    if not os.path.exists(cwd):
        os.makedirs(cwd)

    with open(filePath, 'wb') as f:
        f.write(response.content)

    proc = subprocess.run(cmd, cwd=cwd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, shell=True, env={
        'HOME': cwd
    })

    if proc.returncode != 0:
        return json.dumps({
            'error': proc.stderr.decode('utf-8')
        })
    
    filename = filename.split('.')[0]
    upload_status = upload_file(filename + '.pdf', cwd + filename + '.pdf')
    
    return json.dumps({
        'success': 'Success',
        'upload_status': upload_status,
    })

@app.route('/')
def index():
    return 'Hello World!'

@app.route('/bip/parse', methods=['POST'])
def parse():
    link = request.form.get('url')

    if (link == 'None'):
        return json.dumps({'error': 'URL is required'})

    return bip_parser(link)

@app.route('/bip/parse-file', methods=['POST'])
def parse_file():
    file_path = request.form.get('file_path')

    if (file_path == 'None' or file_path is None):
        return json.dumps({'error': 'File path is required'})

    # Check if file exists
    if not os.path.exists(file_path):
        return json.dumps({'error': 'File not found'})

    try:
        return bip_parser_file(file_path)
    except Exception as e:
        return json.dumps({'error': str(e)})

@app.route('/letter/convert', methods=['POST'])
def convert_letter():
    filename = request.form.get('filename')

    if filename == 'None':
        return json.dumps({
            'error': 'No file uploaded'
        })
    
    return convert_to_pdf(filename)

if __name__ == '__main__':
    app.run(port=5001, host='0.0.0.0')